{"name": "format123-frontend", "private": true, "version": "1.0.0", "description": "Format123 Frontend Application built with Nuxt.js", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "start": "nuxt start", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "nuxt typecheck"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxt/eslint-config": "^0.3.13", "@nuxt/typescript-build": "^3.0.2", "eslint": "^8.57.0", "nuxt": "^3.12.4", "typescript": "^5.5.4", "vue-tsc": "^3.0.4"}, "dependencies": {"@nuxtjs/tailwindcss": "^6.12.1", "@pinia/nuxt": "^0.5.1", "pinia": "^2.2.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "keywords": ["nuxt", "vue", "frontend", "format123"], "author": "Format123 Team", "license": "MIT"}