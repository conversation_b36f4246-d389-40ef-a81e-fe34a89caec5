{"name": "vue-tsc", "version": "3.0.4", "license": "MIT", "files": ["bin", "**/*.js", "**/*.d.ts"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/vuejs/language-tools.git", "directory": "packages/tsc"}, "bin": {"vue-tsc": "./bin/vue-tsc.js"}, "peerDependencies": {"typescript": ">=5.0.0"}, "dependencies": {"@volar/typescript": "2.4.20", "@vue/language-core": "3.0.4"}, "devDependencies": {"@types/node": "^22.10.4"}, "gitHead": "148d386f9779c2de64cdcbd35310e03b36943b05"}