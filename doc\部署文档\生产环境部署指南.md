# 生产环境部署指南

本文档介绍如何将 Format123 Frontend 项目部署到生产环境，包含 Linux 和 Windows 服务器的部署方案。

## 部署方式

项目支持多种部署方式：

1. **静态站点生成 (SSG)** - 适用于内容相对固定的站点
2. **服务端渲染 (SSR)** - 适用于需要动态内容的应用
3. **单页应用 (SPA)** - 适用于客户端渲染的应用

## 环境准备

### Linux 服务器要求

- **操作系统**: Linux (推荐 Ubuntu 20.04+)
- **Node.js**: 版本 >= 18.0.0
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间
- **网络**: 稳定的互联网连接

### Windows 服务器要求

- **操作系统**: Windows Server 2019+ 或 Windows 10/11
- **Node.js**: 版本 >= 18.0.0
- **PowerShell**: 5.1+ 或 PowerShell 7+
- **IIS**: Internet Information Services (可选)
- **内存**: 最少 2GB RAM
- **存储**: 最少 10GB 可用空间

### 域名和 SSL

- 准备域名并配置 DNS 解析
- 申请 SSL 证书（推荐使用 Let's Encrypt）

## 静态站点部署

### 1. 构建静态文件

**Linux/macOS:**
```bash
# 安装依赖
npm install

# 生成静态文件
npm run generate
```

**Windows (PowerShell):**
```powershell
# 安装依赖
npm install

# 生成静态文件
npm run generate
```

### 2. Linux 部署 (Nginx)

安装 Nginx：
```bash
sudo apt update
sudo apt install nginx
```

配置 Nginx：
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # 网站根目录
    root /var/www/format123-frontend;
    index index.html;
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # SPA 路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
}
```

### 3. Windows 部署 (IIS)

#### 安装 IIS 和相关功能

**PowerShell (以管理员身份运行):**
```powershell
# 启用 IIS 功能
Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-HttpErrors, IIS-HttpLogging, IIS-RequestFiltering, IIS-StaticContent, IIS-DefaultDocument

# 安装 URL Rewrite 模块 (需要手动下载安装)
# 从 Microsoft 官网下载 URL Rewrite 模块
```

#### 配置 IIS 站点

1. 打开 IIS 管理器
2. 右键点击"网站" → "添加网站"
3. 配置站点信息：
   - **网站名称**: Format123-Frontend
   - **物理路径**: `C:\inetpub\wwwroot\format123-frontend`
   - **端口**: 80 (HTTP) 或 443 (HTTPS)

#### 创建 web.config 文件

在网站根目录创建 `web.config`：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <rewrite>
      <rules>
        <rule name="SPA Routes" stopProcessing="true">
          <match url=".*" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
          </conditions>
          <action type="Rewrite" url="/" />
        </rule>
      </rules>
    </rewrite>
    <staticContent>
      <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="365.00:00:00" />
    </staticContent>
  </system.webServer>
</configuration>
```

### 4. 部署脚本

#### Linux 部署脚本

创建部署脚本 `deploy.sh`：

```bash
#!/bin/bash

# 项目配置
PROJECT_DIR="/var/www/format123-frontend"
REPO_URL="https://github.com/your-username/format123-frontend.git"
BRANCH="main"

# 备份当前版本
if [ -d "$PROJECT_DIR" ]; then
    sudo cp -r $PROJECT_DIR $PROJECT_DIR.backup.$(date +%Y%m%d_%H%M%S)
fi

# 克隆或更新代码
if [ ! -d "$PROJECT_DIR" ]; then
    sudo git clone $REPO_URL $PROJECT_DIR
else
    cd $PROJECT_DIR
    sudo git fetch origin
    sudo git reset --hard origin/$BRANCH
fi

cd $PROJECT_DIR

# 安装依赖
sudo npm ci --production

# 构建项目
sudo npm run generate

# 设置权限
sudo chown -R www-data:www-data $PROJECT_DIR
sudo chmod -R 755 $PROJECT_DIR

# 重启 Nginx
sudo systemctl reload nginx

echo "部署完成！"
```

#### Windows 部署脚本

创建部署脚本 `deploy.ps1`：

```powershell
# 项目配置
$PROJECT_DIR = "C:\inetpub\wwwroot\format123-frontend"
$REPO_URL = "https://github.com/your-username/format123-frontend.git"
$BRANCH = "main"

# 备份当前版本
if (Test-Path $PROJECT_DIR) {
    $BackupDir = "$PROJECT_DIR.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item -Path $PROJECT_DIR -Destination $BackupDir -Recurse
    Write-Host "已备份到: $BackupDir"
}

# 克隆或更新代码
if (!(Test-Path $PROJECT_DIR)) {
    git clone $REPO_URL $PROJECT_DIR
} else {
    Set-Location $PROJECT_DIR
    git fetch origin
    git reset --hard "origin/$BRANCH"
}

Set-Location $PROJECT_DIR

# 安装依赖
npm ci --production

# 构建项目
npm run generate

# 复制构建文件到 IIS 目录
$DistDir = ".\.output\public"
if (Test-Path $DistDir) {
    Copy-Item -Path "$DistDir\*" -Destination $PROJECT_DIR -Recurse -Force
}

# 重启 IIS 应用程序池 (可选)
# Import-Module WebAdministration
# Restart-WebAppPool -Name "DefaultAppPool"

Write-Host "部署完成！"
```

## 服务端渲染部署

### 1. 构建应用

**Linux/macOS:**
```bash
npm run build
```

**Windows (PowerShell):**
```powershell
npm run build
```

### 2. 使用 PM2 管理进程

#### Linux 安装 PM2：
```bash
npm install -g pm2
```

#### Windows 安装 PM2：
```powershell
npm install -g pm2
```

创建 PM2 配置文件 `ecosystem.config.js`：

```javascript
module.exports = {
  apps: [{
    name: 'format123-frontend',
    script: '.output/server/index.mjs',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      NITRO_HOST: '127.0.0.1'
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
```

#### Linux 启动应用：
```bash
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

#### Windows 启动应用：
```powershell
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

**注意**: 在 Windows 上，PM2 的 `startup` 命令可能需要额外配置。建议使用 Windows 服务或任务计划程序来确保应用在系统重启后自动启动。

### 3. Nginx 反向代理

```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    location / {
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## Docker 部署

### Dockerfile

```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制 package 文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 暴露端口
EXPOSE 3000

# 启动应用
CMD ["npm", "start"]
```

### docker-compose.yml

```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
```

## 监控和维护

### 日志管理

使用 logrotate 管理日志文件：

```bash
# /etc/logrotate.d/format123-frontend
/var/log/format123-frontend/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        systemctl reload nginx
    endscript
}
```

### 性能监控

推荐使用以下工具：

- **PM2 Monitoring**: PM2 内置监控
- **New Relic**: 应用性能监控
- **Datadog**: 基础设施监控

### 备份策略

1. **代码备份**: 使用 Git 版本控制
2. **配置备份**: 定期备份配置文件
3. **数据备份**: 如果有数据库，定期备份数据

## 故障排除

### 常见问题

1. **应用无法启动**
   - 检查 Node.js 版本
   - 检查环境变量配置
   - 查看错误日志

2. **静态资源加载失败**
   - 检查 Nginx 配置
   - 验证文件权限
   - 检查 CDN 配置

3. **性能问题**
   - 启用 Gzip 压缩
   - 配置缓存策略
   - 优化图片资源

### 回滚策略

如果部署出现问题，可以快速回滚：

```bash
# 停止当前应用
pm2 stop format123-frontend

# 恢复备份
sudo rm -rf /var/www/format123-frontend
sudo mv /var/www/format123-frontend.backup.YYYYMMDD_HHMMSS /var/www/format123-frontend

# 重启应用
pm2 start format123-frontend
```

## 安全建议

1. **定期更新依赖包**
2. **使用 HTTPS**
3. **配置防火墙**
4. **定期安全扫描**
5. **限制服务器访问权限**
