// generated by the @nuxtjs/tailwindcss <https://github.com/nuxt-modules/tailwindcss> module at 2025/8/1 00:10:53
import "@nuxtjs/tailwindcss/config-ctx"
import configMerger from "@nuxtjs/tailwindcss/merger";

;
const config = [
{"content":{"files":["C:/E盘/workspace_format/format123_frontend/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/E盘/workspace_format/format123_frontend/components/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/E盘/workspace_format/format123_frontend/layouts/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/E盘/workspace_format/format123_frontend/plugins/**/*.{js,ts,mjs}","C:/E盘/workspace_format/format123_frontend/composables/**/*.{js,ts,mjs}","C:/E盘/workspace_format/format123_frontend/utils/**/*.{js,ts,mjs}","C:/E盘/workspace_format/format123_frontend/pages/**/*.{vue,js,jsx,mjs,ts,tsx}","C:/E盘/workspace_format/format123_frontend/{A,a}pp.{vue,js,jsx,mjs,ts,tsx}","C:/E盘/workspace_format/format123_frontend/{E,e}rror.{vue,js,jsx,mjs,ts,tsx}","C:/E盘/workspace_format/format123_frontend/app.config.{js,ts,mjs}"]}},
{}
].reduce((acc, curr) => configMerger(acc, curr), {});

const resolvedConfig = config;

export default resolvedConfig;