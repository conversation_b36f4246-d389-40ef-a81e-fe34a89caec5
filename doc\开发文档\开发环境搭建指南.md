# 开发环境搭建指南

本文档介绍如何在 Windows 环境下搭建 Format123 Frontend 项目的开发环境。

## 系统要求

### 必需软件

- **Node.js**: 版本 >= 18.0.0
- **npm**: 版本 >= 9.0.0 (随 Node.js 安装)
- **Git for Windows**: 用于版本控制
- **PowerShell**: Windows 10/11 内置，推荐使用 PowerShell 7+

### 推荐软件

- **Visual Studio Code**: 推荐的代码编辑器
- **Vue Language Features (Volar)**: VS Code 的 Vue.js 扩展
- **TypeScript Vue Plugin (Volar)**: VS Code 的 TypeScript 支持
- **Windows Terminal**: 现代化的终端应用程序

## 环境搭建步骤

### 1. 安装 Node.js

从 [Node.js 官网](https://nodejs.org/) 下载并安装最新的 LTS 版本。

验证安装（在 PowerShell 或 CMD 中运行）：
```powershell
node --version
npm --version
```

### 2. 安装 Git for Windows

从 [Git 官网](https://git-scm.com/download/win) 下载并安装 Git for Windows。

### 3. 克隆项目

在 PowerShell 中运行：
```powershell
git clone <项目地址>
cd format123_frontend
```

或使用 CMD：
```cmd
git clone <项目地址>
cd format123_frontend
```

### 4. 安装依赖

```powershell
npm install
```

### 5. 配置环境变量

复制环境变量示例文件：

**PowerShell:**
```powershell
Copy-Item .env.example .env
```

**CMD:**
```cmd
copy .env.example .env
```

根据需要修改 `.env` 文件中的配置。

### 6. 启动开发服务器

```powershell
npm run dev
```

访问 http://localhost:3000 查看应用程序。

## 项目启动

本节详细说明项目的各种启动和构建命令。

### 基本命令

#### 安装依赖
```powershell
npm install
```
**用途**: 安装项目所需的所有依赖包
**预期结果**:
- 创建 `node_modules` 目录
- 生成或更新 `package-lock.json` 文件
- 显示安装的包数量和耗时

#### 启动开发服务器
```powershell
npm run dev
```
**用途**: 启动本地开发服务器，支持热重载
**预期结果**:
- 服务器启动在 http://localhost:3000
- 自动打开浏览器（可选）
- 文件修改时自动刷新页面
- 显示编译状态和错误信息

#### 构建生产版本
```powershell
npm run build
```
**用途**: 构建优化后的生产版本
**预期结果**:
- 在 `.output` 目录生成构建文件
- 代码压缩和优化
- 生成静态资源文件

#### 预览生产版本
```powershell
npm run preview
```
**用途**: 本地预览生产构建版本
**预期结果**:
- 启动生产模式服务器
- 可以测试生产版本的性能和功能

### 其他有用命令

#### 代码检查
```powershell
# 检查代码质量
npm run lint

# 自动修复代码问题
npm run lint:fix
```

#### 类型检查
```powershell
npm run type-check
```

#### 生成静态站点
```powershell
npm run generate
```

### Windows 特定问题解决方案

#### 端口冲突
如果 3000 端口被占用：
```powershell
npm run dev -- --port 3001
```

#### 依赖安装失败
清除缓存并重新安装：
```powershell
# 删除 node_modules 和 package-lock.json
Remove-Item -Recurse -Force node_modules
Remove-Item package-lock.json
npm install
```

或使用 CMD：
```cmd
rmdir /s node_modules
del package-lock.json
npm install
```

#### 权限问题
如果遇到权限错误，以管理员身份运行 PowerShell：
1. 右键点击 PowerShell 图标
2. 选择"以管理员身份运行"
3. 重新执行命令

#### 防火墙问题
Windows 防火墙可能阻止 Node.js：
1. 允许 Node.js 通过 Windows 防火墙
2. 或临时禁用防火墙进行测试

#### 路径问题
确保项目路径不包含中文字符或特殊符号，建议使用英文路径。

## VS Code 配置

### 推荐扩展

在 VS Code 中安装以下扩展：

1. **Vue Language Features (Volar)**
2. **TypeScript Vue Plugin (Volar)**
3. **ESLint**
4. **Tailwind CSS IntelliSense**
5. **Auto Rename Tag**
6. **Bracket Pair Colorizer**

### 工作区设置

在项目根目录创建 `.vscode` 目录和 `settings.json` 文件：

**PowerShell:**
```powershell
New-Item -ItemType Directory -Path .vscode -Force
New-Item -ItemType File -Path .vscode\settings.json
```

**CMD:**
```cmd
mkdir .vscode
type nul > .vscode\settings.json
```

然后编辑 `.vscode\settings.json` 文件，添加以下内容：

```json
{
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "terminal.integrated.defaultProfile.windows": "PowerShell"
}
```

## 环境变量设置

### 临时设置环境变量

**PowerShell:**
```powershell
$env:NODE_ENV = "development"
$env:API_BASE_URL = "http://localhost:8080/api"
```

**CMD:**
```cmd
set NODE_ENV=development
set API_BASE_URL=http://localhost:8080/api
```

### 永久设置环境变量

1. 打开"系统属性" → "高级" → "环境变量"
2. 在"用户变量"或"系统变量"中添加新变量
3. 重启终端使变量生效

## 下一步

- 阅读 [代码规范文档](./代码规范.md)
- 查看 [组件开发指南](./组件开发指南.md)
- 了解 [项目架构](./项目架构.md)
