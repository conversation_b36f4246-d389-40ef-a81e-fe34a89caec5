{"compilerOptions": {"paths": {"nitropack/types": ["../node_modules/nitropack/types"], "nitropack/runtime": ["../node_modules/nitropack/runtime"], "nitropack": ["../node_modules/nitropack"], "defu": ["../node_modules/defu"], "h3": ["../node_modules/h3"], "consola": ["../node_modules/consola"], "ofetch": ["../node_modules/ofetch"], "@unhead/vue": ["../node_modules/@unhead/vue"], "vue": ["../node_modules/vue"], "@vue/runtime-core": ["../node_modules/@vue/runtime-core"], "@vue/compiler-sfc": ["../node_modules/@vue/compiler-sfc"], "vue-router": ["../node_modules/vue-router"], "vue-router/auto-routes": ["../node_modules/vue-router/vue-router-auto-routes"], "unplugin-vue-router/client": ["../node_modules/unplugin-vue-router/client"], "@nuxt/schema": ["../node_modules/@nuxt/schema"], "nuxt": ["../node_modules/nuxt"], "vite/client": ["../node_modules/vite/client"], "~": [".."], "~/*": ["../*"], "@": [".."], "@/*": ["../*"], "~~": [".."], "~~/*": ["../*"], "@@": [".."], "@@/*": ["../*"], "#shared": ["../shared"], "#shared/*": ["../shared/*"], "assets": ["../assets"], "assets/*": ["../assets/*"], "public": ["../public"], "public/*": ["../public/*"], "#app": ["../node_modules/nuxt/dist/app"], "#app/*": ["../node_modules/nuxt/dist/app/*"], "vue-demi": ["../node_modules/nuxt/dist/app/compat/vue-demi"], "pinia": ["../node_modules/pinia/dist/pinia"], "#vue-router": ["../node_modules/vue-router"], "#unhead/composables": ["../node_modules/nuxt/dist/head/runtime/composables/v3"], "#imports": ["./imports"], "#app-manifest": ["./manifest/meta/dev"], "#components": ["./components"], "#build": ["."], "#build/*": ["./*"]}, "esModuleInterop": true, "skipLibCheck": true, "target": "ESNext", "allowJs": true, "resolveJsonModule": true, "moduleDetection": "force", "isolatedModules": true, "verbatimModuleSyntax": true, "strict": true, "noUncheckedIndexedAccess": false, "forceConsistentCasingInFileNames": true, "noImplicitOverride": true, "module": "preserve", "noEmit": true, "lib": ["ESNext", "dom", "dom.iterable", "webworker"], "jsx": "preserve", "jsxImportSource": "vue", "types": [], "moduleResolution": "<PERSON><PERSON><PERSON>", "useDefineForClassFields": true, "noImplicitThis": true, "allowSyntheticDefaultImports": true}, "include": ["../**/*", "../.config/nuxt.*", "./nuxt.d.ts", "../node_modules/runtime", "../node_modules/dist/runtime", ".."], "exclude": ["../dist", "../.data", "../node_modules", "../node_modules/nuxt/node_modules", "../node_modules/@nuxtjs/tailwindcss/node_modules", "../node_modules/@pinia/nuxt/node_modules", "../node_modules/@nuxt/devtools/node_modules", "../node_modules/@nuxt/telemetry/node_modules", "../node_modules/runtime/server", "../node_modules/dist/runtime/server", "dev"]}