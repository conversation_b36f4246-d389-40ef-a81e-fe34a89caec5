{"typesMap": {"jquery": {"match": "jquery(-(\\.?\\d+)+)?(\\.intellisense)?(\\.min)?\\.js$", "types": ["j<PERSON>y"]}, "WinJS": {"match": "^(.*\\/winjs-[.\\d]+)\\/js\\/base\\.js$", "exclude": [["^", 1, "/.*"]], "types": ["winjs"]}, "Kendo": {"match": "^(.*\\/kendo(-ui)?)\\/kendo\\.all(\\.min)?\\.js$", "exclude": [["^", 1, "/.*"]], "types": ["kendo-ui"]}, "Office Nuget": {"match": "^(.*\\/office\\/1)\\/excel-\\d+\\.debug\\.js$", "exclude": [["^", 1, "/.*"]], "types": ["office"]}, "References": {"match": "^(.*\\/_references\\.js)$", "exclude": [["^", 1, "$"]]}, "Datatables.net": {"match": "^.*\\/(j<PERSON>y\\.)?dataTables(\\.all)?(\\.min)?\\.js$", "types": ["datatables.net"]}, "Ace": {"match": "^(.*)\\/ace.js", "exclude": [["^", 1, "/.*"]], "types": ["ace"]}}, "simpleMap": {"accounting": "accounting", "ace.js": "ace", "ag-grid": "ag-grid", "alertify": "alertify", "alt": "alt", "amcharts.js": "amcharts", "amplify": "amplifyjs", "angular": "angular", "angular-bootstrap-lightbox": "angular-bootstrap-lightbox", "angular-cookie": "angular-cookie", "angular-file-upload": "angular-file-upload", "angularfire": "angularfire", "angular-gettext": "angular-gettext", "angular-google-analytics": "angular-google-analytics", "angular-local-storage": "angular-local-storage", "angularLocalStorage": "angularLocalStorage", "angular-scroll": "angular-scroll", "angular-spinner": "angular-spinner", "angular-strap": "angular-strap", "angulartics": "angulartics", "angular-toastr": "angular-toastr", "angular-translate": "angular-translate", "angular-ui-router": "angular-ui-router", "angular-ui-tree": "angular-ui-tree", "angular-wizard": "angular-wizard", "async": "async", "atmosphere": "atmosphere", "aws-sdk": "aws-sdk", "aws-sdk-js": "aws-sdk", "axios": "axios", "backbone": "backbone", "backbone.layoutmanager": "backbone.layoutmanager", "backbone.paginator": "backbone.paginator", "backbone.radio": "backbone.radio", "backbone-associations": "backbone-associations", "backbone-relational": "backbone-relational", "backgrid": "backgrid", "Bacon": "baconjs", "benchmark": "benchmark", "blazy": "blazy", "bliss": "blissfuljs", "bluebird": "bluebird", "body-parser": "body-parser", "bootbox": "bootbox", "bootstrap": "bootstrap", "bootstrap-editable": "x-editable", "bootstrap-maxlength": "bootstrap-maxlength", "bootstrap-notify": "bootstrap-notify", "bootstrap-slider": "bootstrap-slider", "bootstrap-switch": "bootstrap-switch", "bowser": "bowser", "breeze": "breeze", "browserify": "browserify", "bson": "bson", "c3": "c3", "canvasjs": "canvasjs", "chai": "chai", "chalk": "chalk", "chance": "chance", "chartist": "chartist", "cheerio": "cheerio", "chokidar": "chokidar", "chosen.jquery": "chosen", "chroma": "chroma-js", "ckeditor.js": "ckeditor", "cli-color": "cli-color", "clipboard": "clipboard", "codemirror": "codemirror", "colors": "colors", "commander": "commander", "commonmark": "commonmark", "compression": "compression", "confidence": "confidence", "connect": "connect", "Control.FullScreen": "leaflet.fullscreen", "cookie": "cookie", "cookie-parser": "cookie-parser", "cookies": "cookies", "core": "core-js", "core-js": "core-js", "crossfilter": "crossfilter", "crossroads": "crossroads", "css": "css", "ct-ui-router-extras": "ui-router-extras", "d3": "d3", "dagre-d3": "dagre-d3", "dat.gui": "dat-gui", "debug": "debug", "deep-diff": "deep-diff", "Dexie": "dexie", "dialogs": "angular-dialog-service", "dojo.js": "dojo", "doT": "dot", "dragula": "dragula", "drop": "drop", "dropbox": "dropboxjs", "dropzone": "dropzone", "Dts Name": "Dts Name", "dust-core": "<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "easeljs": "easeljs", "ejs": "ejs", "ember": "ember", "envify": "envify", "epiceditor": "epiceditor", "es6-promise": "es6-promise", "ES6-Promise": "es6-promise", "es6-shim": "es6-shim", "expect": "expect", "express": "express", "express-session": "express-session", "ext-all.js": "extjs", "extend": "extend", "fabric": "fabricjs", "faker": "faker", "fastclick": "fastclick", "favico": "favico.js", "featherlight": "featherlight", "FileSaver": "FileSaver", "fingerprint": "fingerprintjs", "fixed-data-table": "fixed-data-table", "flickity.pkgd": "flickity", "flight": "flight", "flow": "flowjs", "Flux": "flux", "formly": "angular-formly", "foundation": "foundation", "fpsmeter": "fpsmeter", "fuse": "fuse", "generator": "yeoman-generator", "gl-matrix": "gl-matrix", "globalize": "globalize", "graceful-fs": "graceful-fs", "gridstack": "gridstack", "gulp": "gulp", "gulp-rename": "gulp-rename", "gulp-uglify": "gulp-uglify", "gulp-util": "gulp-util", "hammer": "<PERSON><PERSON><PERSON>", "handlebars": "handlebars", "hasher": "hasher", "he": "he", "hello.all": "hellojs", "highcharts.js": "highcharts", "highlight": "highlightjs", "history": "history", "History": "history", "hopscotch": "hopscotch", "hotkeys": "angular-hotkeys", "html2canvas": "html2canvas", "humane": "humane", "i18next": "i18next", "icheck": "icheck", "impress": "impress", "incremental-dom": "incremental-dom", "Inquirer": "inquirer", "insight": "insight", "interact": "interactjs", "intercom": "intercomjs", "intro": "intro.js", "ion.rangeSlider": "ion.rangeSlider", "ionic": "ionic", "is": "is_js", "iscroll": "iscroll", "jade": "jade", "jasmine": "jasmine", "joint": "jointjs", "jquery": "j<PERSON>y", "jquery.address": "jquery.address", "jquery.are-you-sure": "j<PERSON>y.are-you-sure", "jquery.blockUI": "jquery.blockUI", "jquery.bootstrap.wizard": "jquery.bootstrap.wizard", "jquery.bootstrap-touchspin": "bootstrap-touchspin", "jquery.color": "jquery.color", "jquery.colorbox": "jquery.colorbox", "jquery.contextMenu": "jquery.contextMenu", "jquery.cookie": "jquery.cookie", "jquery.customSelect": "jquery.customSelect", "jquery.cycle.all": "jquery.cycle", "jquery.cycle2": "jquery.cycle2", "jquery.dataTables": "jquery.dataTables", "jquery.dropotron": "jquery.dropotron", "jquery.fancybox.pack.js": "fancybox", "jquery.fancytree-all": "jquery.fancytree", "jquery.fileupload": "jquery.fileupload", "jquery.flot": "flot", "jquery.form": "jquery.form", "jquery.gridster": "jquery.gridster", "jquery.handsontable.full": "jquery-handsontable", "jquery.joyride": "jquery.joyride", "jquery.jqGrid": "jqgrid", "jquery.mmenu": "jquery.mmenu", "jquery.mockjax": "j<PERSON>y-mockjax", "jquery.noty": "jquery.noty", "jquery.payment": "jquery.payment", "jquery.pjax": "jquery.pjax", "jquery.placeholder": "jquery.placeholder", "jquery.qrcode": "jquery.qrcode", "jquery.qtip": "qtip2", "jquery.raty": "raty", "jquery.scrollTo": "jquery.scrollTo", "jquery.signalR": "signalr", "jquery.simplemodal": "jquery.simplemodal", "jquery.timeago": "jquery.timeago", "jquery.tinyscrollbar": "jquery.tinyscrollbar", "jquery.tipsy": "jquery.tipsy", "jquery.tooltipster": "tooltipster", "jquery.transit": "jquery.transit", "jquery.uniform": "jquery.uniform", "jquery.watch": "watch", "jquery-sortable": "jquery-sortable", "jquery-ui": "jqueryui", "js.cookie": "js-cookie", "js-data": "js-data", "js-data-angular": "js-data-angular", "js-data-http": "js-data-http", "jsdom": "jsdom", "jsnlog": "jsnlog", "json5": "json5", "jspdf": "jspdf", "jsrender": "js<PERSON><PERSON>", "js-signals": "js-signals", "jstorage": "jstorage", "jstree": "jstree", "js-yaml": "js-yaml", "jszip": "j<PERSON><PERSON>", "katex": "katex", "kefir": "kefir", "keymaster": "keymaster", "keypress": "keypress", "kinetic": "<PERSON><PERSON><PERSON>", "knockback": "knockback", "knockout": "knockout", "knockout.mapping": "knockout.mapping", "knockout.validation": "knockout.validation", "knockout-paging": "knockout-paging", "knockout-pre-rendered": "knockout-pre-rendered", "ladda": "ladda", "later": "later", "lazy": "lazy.js", "Leaflet.Editable": "leaflet-editable", "leaflet.js": "leaflet", "less": "less", "linq": "linq", "loading-bar": "angular-loading-bar", "lodash": "lodash", "log4javascript": "log4javascript", "loglevel": "loglevel", "lokijs": "lokijs", "lovefield": "lovefield", "lunr": "lunr", "lz-string": "lz-string", "mailcheck": "mailcheck", "maquette": "maquette", "marked": "marked", "math": "mathjs", "MathJax.js": "mathjax", "matter": "matter-js", "md5": "blueimp-md5", "md5.js": "crypto-js", "messenger": "messenger", "method-override": "method-override", "minimatch": "minimatch", "minimist": "minimist", "mithril": "mithril", "mobile-detect": "mobile-detect", "mocha": "mocha", "mock-ajax": "jasmine-ajax", "modernizr": "modernizr", "Modernizr": "Modernizr", "moment": "moment", "moment-range": "moment-range", "moment-timezone": "moment-timezone", "mongoose": "mongoose", "morgan": "morgan", "mousetrap": "mousetrap", "ms": "ms", "mustache": "mustache", "native.history": "history", "nconf": "nconf", "ncp": "ncp", "nedb": "nedb", "ng-cordova": "ng-<PERSON>ova", "ngDialog": "ng-dialog", "ng-flow-standalone": "ng-flow", "ng-grid": "ng-grid", "ng-i18next": "ng-i18next", "ng-table": "ng-table", "node_redis": "redis", "node-clone": "clone", "node-fs-extra": "fs-extra", "node-glob": "glob", "Nodemailer": "nodemailer", "node-mime": "mime", "node-mkdirp": "mkdirp", "node-mongodb-native": "mongodb", "node-mysql": "mysql", "node-open": "open", "node-optimist": "optimist", "node-progress": "progress", "node-semver": "semver", "node-tar": "tar", "node-uuid": "node-uuid", "node-xml2js": "xml2js", "nopt": "nopt", "notify": "notify", "nouislider": "<PERSON>ui<PERSON><PERSON><PERSON>", "npm": "npm", "nprogress": "nprogress", "numbro": "<PERSON>ro", "numeral": "numeraljs", "nunjucks": "nunjucks", "nv.d3": "nvd3", "object-assign": "object-assign", "oboe-browser": "oboe", "office": "office-js", "offline": "offline-js", "onsenui": "on<PERSON><PERSON>", "OpenLayers.js": "openlayers", "openpgp": "openpgp", "p2": "p2", "packery.pkgd": "packery", "page": "page", "pako": "pako", "papaparse": "<PERSON><PERSON><PERSON><PERSON>", "passport": "passport", "passport-local": "passport-local", "path": "pathjs", "pdfkit": "pdfkit", "peer": "peerjs", "peg": "pegjs", "photoswipe": "photoswipe", "picker.js": "pickadate", "pikaday": "pikaday", "pixi": "pixi.js", "platform": "platform", "Please": "pleasejs", "plottable": "plottable", "polymer": "polymer", "postal": "postal", "preloadjs": "preloadjs", "progress": "progress", "purify": "dompurify", "purl": "purl", "q": "q", "qs": "qs", "qunit": "qunit", "ractive": "ractive", "rangy-core": "rangy", "raphael": "<PERSON><PERSON><PERSON>", "raven": "raven<PERSON>s", "react": "react", "react-bootstrap": "react-bootstrap", "react-intl": "react-intl", "react-redux": "react-redux", "ReactRouter": "react-router", "ready": "<PERSON><PERSON><PERSON><PERSON>", "redux": "redux", "request": "request", "require": "require", "restangular": "restangular", "reveal": "reveal", "rickshaw": "rickshaw", "rimraf": "<PERSON><PERSON><PERSON>", "rivets": "rivets", "rx": "rx", "rx.angular": "rx-angular", "sammy": "sammy<PERSON>s", "SAT": "sat", "sax-js": "sax", "screenfull": "screenfull", "seedrandom": "seedrandom", "select2": "select2", "selectize": "selectize", "serve-favicon": "serve-favicon", "serve-static": "serve-static", "shelljs": "<PERSON><PERSON>s", "should": "should", "showdown": "showdown", "sigma": "sigma<PERSON>s", "signature_pad": "signature_pad", "sinon": "sinon", "sjcl": "sjcl", "slick": "slick-carousel", "smoothie": "smoothie", "socket.io": "socket.io", "socket.io-client": "socket.io-client", "sockjs": "sockjs-client", "sortable": "angular-ui-sortable", "soundjs": "soundjs", "source-map": "source-map", "spectrum": "spectrum", "spin": "spin", "sprintf": "sprintf", "stampit": "stampit", "state-machine": "state-machine", "Stats": "stats", "store": "storejs", "string": "string", "string_score": "string_score", "strophe": "strophe", "stylus": "stylus", "sugar": "sugar", "superagent": "superagent", "svg": "svgjs", "svg-injector": "svg-injector", "swfobject": "swfobject", "swig": "swig", "swipe": "swipe", "swiper": "swiper", "system.js": "systemjs", "tether": "tether", "three": "threejs", "through": "through", "through2": "through2", "timeline": "timelinejs", "tinycolor": "tinycolor", "tmhDynamicLocale": "angular-dynamic-locale", "toaster": "angularjs-toaster", "toastr": "toastr", "tracking": "tracking", "trunk8": "trunk8", "turf": "turf", "tweenjs": "tweenjs", "TweenMax": "gsap", "twig": "twig", "twix": "twix", "typeahead.bundle": "typeahead", "typescript": "typescript", "ui": "winjs", "ui-bootstrap-tpls": "angular-ui-bootstrap", "ui-grid": "ui-grid", "uikit": "uikit", "underscore": "underscore", "underscore.string": "underscore.string", "update-notifier": "update-notifier", "url": "jsurl", "UUID": "uuid", "validator": "validator", "vega": "vega", "vex": "vex-js", "video": "videojs", "vue": "vue", "vue-router": "vue-router", "webtorrent": "webtorrent", "when": "when", "winston": "winston", "wrench-js": "wrench", "ws": "ws", "xlsx": "xlsx", "xml2json": "x2js", "xmlbuilder-js": "xmlbuilder", "xregexp": "xregexp", "yargs": "yargs", "yosay": "yosay", "yui": "yui", "yui3": "yui", "zepto": "zepto", "ZeroClipboard": "zeroclipboard", "ZSchema-browser": "z-schema"}}