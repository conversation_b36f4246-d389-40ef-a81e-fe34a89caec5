"use strict";Object.defineProperty(exports, "__esModule", { value: true });exports.default = void 0; // https://nuxt.com/docs/api/configuration/nuxt-config
var _default = exports.default = defineNuxtConfig({
  devtools: { enabled: true },

  // CSS框架配置
  modules: [
  '@nuxtjs/tailwindcss',
  '@pinia/nuxt'],


  // TypeScript配置
  typescript: {
    strict: true,
    typeCheck: true
  },

  // 应用配置
  app: {
    head: {
      title: 'Format123 Frontend',
      meta: [
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1' },
      { name: 'description', content: 'Format123 Frontend Application' }],

      link: [
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' }]

    }
  },

  // 开发服务器配置
  devServer: {
    port: 3000,
    host: 'localhost'
  },

  // 构建配置
  build: {
    transpile: []
  },

  // 运行时配置
  runtimeConfig: {
    // 私有配置（仅在服务器端可用）
    apiSecret: process.env.API_SECRET || '',

    // 公共配置（客户端和服务器端都可用）
    public: {
      apiBase: process.env.API_BASE_URL || '/api',
      appName: 'Format123 Frontend'
    }
  },

  // CSS配置
  css: [
  '~/assets/css/main.css'],


  // 组件自动导入
  components: [
  {
    path: '~/components',
    pathPrefix: false
  }],


  // 页面配置
  pages: true,

  // 实验性功能
  experimental: {
    payloadExtraction: false
  }
}); /* v9-5bec5be70d099383 */
